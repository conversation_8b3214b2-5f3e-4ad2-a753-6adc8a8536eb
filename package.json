{"name": "telegram-marketplace-bot", "version": "1.0.0", "description": "Multi-language Telegram marketplace bot for selling channels, numbers, and services", "main": "src/bot.js", "scripts": {"start": "node src/bot.js", "dev": "nodemon src/bot.js", "test": "jest", "setup": "node src/setup.js"}, "keywords": ["telegram", "bot", "marketplace", "multilingual", "channels", "numbers", "services"], "author": "Market Bot Developer", "license": "MIT", "dependencies": {"node-telegram-bot-api": "^0.64.0", "sqlite3": "^5.1.6", "dotenv": "^16.3.1", "uuid": "^9.0.1", "moment": "^2.29.4"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0"}, "engines": {"node": ">=16.0.0"}}