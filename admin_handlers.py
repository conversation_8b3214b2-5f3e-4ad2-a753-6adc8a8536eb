from pyrogram.types import Callback<PERSON><PERSON>y, InlineKeyboardMarkup, InlineKeyboardButton
from database import db
from translations import get_text
from keyboards import admin_items_keyboard, confirm_keyboard, back_keyboard
import json

class AdminHandlers:
    def __init__(self, bot_handlers):
        self.bot_handlers = bot_handlers
        self.app = bot_handlers.app
    
    async def handle_admin_edit_callback(self, callback_query: CallbackQuery, data):
        """Handle admin edit callbacks"""
        user_id = callback_query.from_user.id
        language = db.get_user_language(user_id)
        
        parts = data.split("_")
        action = parts[2]  # edit
        item_type = parts[3]  # services/channels/numbers
        item_id = int(parts[4])
        
        if action == "edit":
            await self.show_edit_options(callback_query, item_type, item_id)
        elif action == "delete":
            await self.confirm_delete(callback_query, item_type, item_id)
        elif action == "toggle":
            await self.toggle_item_status(callback_query, item_type, item_id)
    
    async def show_edit_options(self, callback_query, item_type, item_id):
        """Show edit options for an item"""
        user_id = callback_query.from_user.id
        language = db.get_user_language(user_id)
        
        keyboard = [
            [
                InlineKeyboardButton("✏️ تعديل" if language == "ar" else "✏️ Edit" if language == "en" else "✏️ ویرایش", 
                                   callback_data=f"admin_start_edit_{item_type}_{item_id}"),
                InlineKeyboardButton("🗑️ حذف" if language == "ar" else "🗑️ Delete" if language == "en" else "🗑️ حذف", 
                                   callback_data=f"admin_confirm_delete_{item_type}_{item_id}")
            ],
            [
                InlineKeyboardButton("🔄 تفعيل/تعطيل" if language == "ar" else "🔄 Toggle" if language == "en" else "🔄 تغییر وضعیت", 
                                   callback_data=f"admin_toggle_item_{item_type}_{item_id}")
            ],
            [InlineKeyboardButton(get_text("back", language), callback_data=f"admin_edit_{item_type}s")]
        ]
        
        # Get item details
        if item_type == "service":
            item = db.get_service(item_id)
            title = item.get("title", {}).get(language, "") if item else ""
        elif item_type == "channel":
            item = db.get_channel(item_id)
            title = item.get("title", {}).get(language, "") if item else ""
        elif item_type == "number":
            item = db.get_number(item_id)
            title = item.get("number", "") if item else ""
        
        text = f"إدارة: {title}" if language == "ar" else f"Manage: {title}" if language == "en" else f"مدیریت: {title}"
        
        await callback_query.edit_message_text(text, reply_markup=InlineKeyboardMarkup(keyboard))
    
    async def confirm_delete(self, callback_query, item_type, item_id):
        """Show delete confirmation"""
        user_id = callback_query.from_user.id
        language = db.get_user_language(user_id)
        
        text = f"هل أنت متأكد من حذف هذا العنصر؟" if language == "ar" else "Are you sure you want to delete this item?" if language == "en" else "آیا از حذف این آیتم مطمئن هستید؟"
        
        keyboard = [
            [
                InlineKeyboardButton("✅ نعم" if language == "ar" else "✅ Yes" if language == "en" else "✅ بله", 
                                   callback_data=f"admin_delete_confirmed_{item_type}_{item_id}"),
                InlineKeyboardButton("❌ لا" if language == "ar" else "❌ No" if language == "en" else "❌ خیر", 
                                   callback_data=f"admin_edit_{item_type}_{item_id}")
            ]
        ]
        
        await callback_query.edit_message_text(text, reply_markup=InlineKeyboardMarkup(keyboard))
    
    async def delete_item(self, callback_query, item_type, item_id):
        """Delete an item"""
        user_id = callback_query.from_user.id
        language = db.get_user_language(user_id)
        
        try:
            if item_type == "service":
                db.delete_service(item_id)
                success_msg = get_text("service_deleted", language)
            elif item_type == "channel":
                db.delete_channel(item_id)
                success_msg = get_text("channel_deleted", language)
            elif item_type == "number":
                db.delete_number(item_id)
                success_msg = get_text("number_deleted", language)
            
            await callback_query.edit_message_text(
                success_msg,
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton(get_text("back", language), callback_data="admin")]
                ])
            )
        except Exception as e:
            await callback_query.answer(get_text("error_occurred", language))
    
    async def toggle_item_status(self, callback_query, item_type, item_id):
        """Toggle item status (active/inactive)"""
        user_id = callback_query.from_user.id
        language = db.get_user_language(user_id)
        
        try:
            if item_type == "service":
                new_status = db.toggle_service_status(item_id)
            elif item_type == "channel":
                new_status = db.toggle_channel_status(item_id)
            elif item_type == "number":
                new_status = db.toggle_number_status(item_id)
            
            status_text = "مفعل" if new_status == "active" else "معطل" if language == "ar" else "Active" if new_status == "active" else "Inactive" if language == "en" else "فعال" if new_status == "active" else "غیرفعال"
            
            await callback_query.edit_message_text(
                f"{get_text('status_toggled', language)}\n\n🔄 الحالة الجديدة: {status_text}",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton(get_text("back", language), callback_data=f"admin_edit_{item_type}_{item_id}")]
                ])
            )
        except Exception as e:
            await callback_query.answer(get_text("error_occurred", language))
    
    async def handle_toggle_callback(self, callback_query, data):
        """Handle toggle callbacks"""
        user_id = callback_query.from_user.id
        language = db.get_user_language(user_id)
        
        if data == "admin_toggle_services":
            items = db.get_all_services("active") + db.get_all_services("inactive")
            item_type = "services"
        elif data == "admin_toggle_channels":
            items = db.get_all_channels("active") + db.get_all_channels("inactive")
            item_type = "channels"
        elif data == "admin_toggle_numbers":
            items = db.get_all_numbers("active") + db.get_all_numbers("inactive")
            item_type = "numbers"
        
        if not items:
            text = f"لا توجد عناصر متوفرة" if language == "ar" else "No items available" if language == "en" else "آیتمی موجود نیست"
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton(get_text("back", language), callback_data="admin")]
            ])
        else:
            text = f"اختر عنصر لتفعيل/تعطيل:" if language == "ar" else "Choose item to toggle:" if language == "en" else "آیتم را برای تغییر وضعیت انتخاب کنید:"
            keyboard = admin_items_keyboard(items, item_type[:-1], "toggle", language)  # Remove 's' from item_type
        
        await callback_query.edit_message_text(text, reply_markup=keyboard)
    
    async def start_edit_item(self, callback_query, item_type, item_id):
        """Start editing an item"""
        user_id = callback_query.from_user.id
        language = db.get_user_language(user_id)
        
        # Store edit context
        edit_context = {
            "item_type": item_type,
            "item_id": item_id,
            "field": "title_ar"
        }
        db.redis.set(f"edit_context:{user_id}", json.dumps(edit_context))
        
        # Get current item data
        if item_type == "service":
            item = db.get_service(item_id)
        elif item_type == "channel":
            item = db.get_channel(item_id)
        elif item_type == "number":
            item = db.get_number(item_id)
        
        if item:
            # Store current data for editing
            db.redis.set(f"edit_data:{user_id}", json.dumps(item))
            
            # Start with title editing
            if item_type in ["service", "channel"]:
                db.redis.set(f"user_state:{user_id}", f"edit_{item_type}_title_ar")
                await callback_query.edit_message_text(
                    get_text("enter_title_ar", language),
                    reply_markup=back_keyboard(language)
                )
            else:  # number
                db.redis.set(f"user_state:{user_id}", f"edit_number_number")
                await callback_query.edit_message_text(
                    get_text("enter_number", language),
                    reply_markup=back_keyboard(language)
                )
        else:
            await callback_query.answer(get_text("error_occurred", language))
