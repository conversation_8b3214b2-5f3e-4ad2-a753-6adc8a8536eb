from pyrogram import Client
from config import API_ID, API_HASH, BOT_TOKEN
from handlers import BotHandlers
import asyncio

async def main():
    app = Client(
        "market_bot",
        api_id=API_ID,
        api_hash=API_HASH,
        bot_token=BOT_TOKEN
    )
    
    # Initialize handlers
    handlers = BotHandlers(app)
    
    print("🤖 Market Bot is starting...")
    
    async with app:
        print("✅ Market Bot is running!")
        await app.idle()

if __name__ == "__main__":
    asyncio.run(main())
