import requests
from googletrans import Translator
import time

class AutoTranslator:
    def __init__(self):
        self.translator = Translator()
        self.cache = {}
    
    def translate_text(self, text, target_lang):
        """Translate text to target language"""
        if not text or not text.strip():
            return text
        
        # Check cache first
        cache_key = f"{text}_{target_lang}"
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        try:
            # Add delay to avoid rate limiting
            time.sleep(0.5)
            
            result = self.translator.translate(text, dest=target_lang)
            translated_text = result.text
            
            # Cache the result
            self.cache[cache_key] = translated_text
            
            return translated_text
        except Exception as e:
            print(f"Translation error: {e}")
            return text  # Return original text if translation fails
    
    def auto_translate_content(self, arabic_text):
        """Auto translate Arabic text to English and Persian"""
        if not arabic_text or not arabic_text.strip():
            return {
                "ar": arabic_text,
                "en": arabic_text,
                "fa": arabic_text
            }
        
        try:
            # Translate to English
            english_text = self.translate_text(arabic_text, 'en')
            
            # Translate to Persian
            persian_text = self.translate_text(arabic_text, 'fa')
            
            return {
                "ar": arabic_text,
                "en": english_text,
                "fa": persian_text
            }
        except Exception as e:
            print(f"Auto translation error: {e}")
            # Return Arabic text for all languages if translation fails
            return {
                "ar": arabic_text,
                "en": arabic_text,
                "fa": arabic_text
            }
    
    def translate_service_data(self, title_ar, description_ar):
        """Translate service data"""
        title_translations = self.auto_translate_content(title_ar)
        description_translations = self.auto_translate_content(description_ar)
        
        return {
            "title": title_translations,
            "description": description_translations
        }
    
    def translate_channel_data(self, title_ar, description_ar, category_ar):
        """Translate channel data"""
        title_translations = self.auto_translate_content(title_ar)
        description_translations = self.auto_translate_content(description_ar)
        category_translations = self.auto_translate_content(category_ar)
        
        return {
            "title": title_translations,
            "description": description_translations,
            "category": category_translations
        }
    
    def translate_number_data(self, country_ar, operator_ar):
        """Translate number data"""
        country_translations = self.auto_translate_content(country_ar)
        operator_translations = self.auto_translate_content(operator_ar)
        
        return {
            "country": country_translations,
            "operator": operator_translations
        }

# Global translator instance
translator = AutoTranslator()
