from pyrogram import Client, filters
from pyrogram.types import Message, CallbackQuery
from database import db
from translations import get_text
from keyboards import *
from config import ADMIN_ID
from translator import translator
import math
import json

class BotHandlers:
    def __init__(self, app: Client):
        self.app = app
        self.setup_handlers()
    
    def setup_handlers(self):
        # Command handlers
        self.app.on_message(filters.command("start"))(self.start_command)
        self.app.on_message(filters.command("menu"))(self.menu_command)
        self.app.on_message(filters.command("services"))(self.services_command)
        self.app.on_message(filters.command("channels"))(self.channels_command)
        self.app.on_message(filters.command("numbers"))(self.numbers_command)
        self.app.on_message(filters.command("contact"))(self.contact_command)
        self.app.on_message(filters.command("language"))(self.language_command)
        self.app.on_message(filters.command("refresh"))(self.refresh_command)
        
        # Admin commands
        self.app.on_message(filters.command("admin") & filters.user(ADMIN_ID))(self.admin_command)
        self.app.on_message(filters.command("add_service") & filters.user(ADMIN_ID))(self.add_service_command)
        self.app.on_message(filters.command("add_channel") & filters.user(ADMIN_ID))(self.add_channel_command)
        self.app.on_message(filters.command("add_number") & filters.user(ADMIN_ID))(self.add_number_command)
        self.app.on_message(filters.command("broadcast") & filters.user(ADMIN_ID))(self.broadcast_command)
        self.app.on_message(filters.command("users") & filters.user(ADMIN_ID))(self.users_command)
        
        # Callback handlers
        self.app.on_callback_query()(self.callback_handler)
        
        # Message handlers
        self.app.on_message(filters.text & filters.private)(self.text_handler)
    
    async def start_command(self, client: Client, message: Message):
        user_id = message.from_user.id
        user = db.get_user(user_id)
        
        if not user:
            # New user - show language selection
            await message.reply_text(
                "🌐 Choose your language / اختر لغتك / زبان خود را انتخاب کنید:",
                reply_markup=language_keyboard()
            )
        else:
            # Existing user - show main menu
            language = db.get_user_language(user_id)
            await message.reply_text(
                get_text("welcome", language),
                reply_markup=main_menu_keyboard(language)
            )
    
    async def menu_command(self, client: Client, message: Message):
        user_id = message.from_user.id
        language = db.get_user_language(user_id)
        await message.reply_text(
            get_text("main_menu", language),
            reply_markup=main_menu_keyboard(language)
        )
    
    async def services_command(self, client: Client, message: Message):
        await self.show_services(message.from_user.id, message)
    
    async def channels_command(self, client: Client, message: Message):
        await self.show_channels(message.from_user.id, message)
    
    async def numbers_command(self, client: Client, message: Message):
        await self.show_numbers(message.from_user.id, message)
    
    async def contact_command(self, client: Client, message: Message):
        user_id = message.from_user.id
        language = db.get_user_language(user_id)
        await message.reply_text(
            get_text("contact_message", language),
            reply_markup=cancel_keyboard(language)
        )
        # Set user state for contact message
        db.redis.set(f"user_state:{user_id}", "waiting_contact_message")
    
    async def language_command(self, client: Client, message: Message):
        await message.reply_text(
            "🌐 Choose your language / اختر لغتك / زبان خود را انتخاب کنید:",
            reply_markup=language_keyboard()
        )
    
    async def refresh_command(self, client: Client, message: Message):
        user_id = message.from_user.id
        language = db.get_user_language(user_id)
        await message.reply_text(
            get_text("main_menu", language),
            reply_markup=main_menu_keyboard(language)
        )
    
    async def admin_command(self, client: Client, message: Message):
        language = db.get_user_language(message.from_user.id)
        await message.reply_text(
            get_text("admin_panel", language),
            reply_markup=admin_main_keyboard(language)
        )
    
    async def add_service_command(self, client: Client, message: Message):
        await self.start_add_service(message.from_user.id, message)
    
    async def add_channel_command(self, client: Client, message: Message):
        await self.start_add_channel(message.from_user.id, message)
    
    async def add_number_command(self, client: Client, message: Message):
        await self.start_add_number(message.from_user.id, message)
    
    async def broadcast_command(self, client: Client, message: Message):
        user_id = message.from_user.id
        language = db.get_user_language(user_id)
        await message.reply_text(
            get_text("enter_broadcast_message", language),
            reply_markup=cancel_keyboard(language)
        )
        db.redis.set(f"user_state:{user_id}", "waiting_broadcast_message")
    
    async def users_command(self, client: Client, message: Message):
        await self.show_stats(message.from_user.id, message)
    
    async def callback_handler(self, client: Client, callback_query: CallbackQuery):
        data = callback_query.data
        user_id = callback_query.from_user.id
        language = db.get_user_language(user_id)
        
        try:
            if data.startswith("lang_"):
                # Language selection
                selected_language = data.split("_")[1]
                if db.get_user(user_id):
                    db.set_user_language(user_id, selected_language)
                else:
                    db.add_user(user_id, selected_language)
                
                await callback_query.edit_message_text(
                    get_text("welcome", selected_language),
                    reply_markup=main_menu_keyboard(selected_language)
                )
            
            elif data == "main_menu":
                await callback_query.edit_message_text(
                    get_text("main_menu", language),
                    reply_markup=main_menu_keyboard(language)
                )
            
            elif data == "services":
                await self.show_services(user_id, callback_query)
            
            elif data == "channels":
                await self.show_channels(user_id, callback_query)
            
            elif data == "numbers":
                await self.show_numbers(user_id, callback_query)
            
            elif data == "contact":
                await callback_query.edit_message_text(
                    get_text("contact_message", language),
                    reply_markup=back_keyboard(language)
                )
                db.redis.set(f"user_state:{user_id}", "waiting_contact_message")
            
            elif data == "settings":
                await callback_query.edit_message_text(
                    get_text("settings", language),
                    reply_markup=settings_keyboard(language)
                )
            
            elif data == "change_language":
                await callback_query.edit_message_text(
                    "🌐 Choose your language / اختر لغتك / زبان خود را انتخاب کنید:",
                    reply_markup=language_keyboard()
                )
            
            elif data == "refresh":
                await callback_query.edit_message_text(
                    get_text("main_menu", language),
                    reply_markup=main_menu_keyboard(language)
                )
            
            # Admin callbacks
            elif data == "admin" and user_id == ADMIN_ID:
                await callback_query.edit_message_text(
                    get_text("admin_panel", language),
                    reply_markup=admin_main_keyboard(language)
                )
            
            elif data.startswith("admin_") and user_id == ADMIN_ID:
                await self.handle_admin_callback(callback_query, data)

            # Handle admin edit/delete/toggle callbacks
            elif data.startswith(("admin_edit_", "admin_delete_", "admin_toggle_", "admin_start_", "admin_confirm_", "confirm_", "admin_search_", "admin_pending_", "complete_coupon_", "cancel_coupon_")) and user_id == ADMIN_ID:
                await self.handle_admin_action_callback(callback_query, data)

            # Handle buy callbacks
            elif data.startswith("buy_"):
                await self.handle_buy_callback(callback_query, data)
            
            # Item view callbacks
            elif data.startswith(("services_view_", "channels_view_", "numbers_view_")):
                await self.show_item_details(callback_query, data)

            # Buy callbacks for all item types
            elif data.startswith(("services_buy_", "channels_buy_", "numbers_buy_")):
                await self.handle_buy_callback(callback_query, data)
            
            # Pagination callbacks
            elif "_page_" in data:
                await self.handle_pagination(callback_query, data)
            
            await callback_query.answer()
            
        except Exception as e:
            print(f"Error in callback handler: {e}")
            await callback_query.answer(get_text("error_occurred", language))
    
    async def text_handler(self, client: Client, message: Message):
        user_id = message.from_user.id
        language = db.get_user_language(user_id)
        user_state = db.redis.get(f"user_state:{user_id}")
        
        if not user_state:
            return
        
        text = message.text
        
        # Cancel operation
        if text in ["إلغاء", "Cancel", "لغو"]:
            db.redis.delete(f"user_state:{user_id}")
            await message.reply_text(
                get_text("operation_cancelled", language),
                reply_markup=main_menu_keyboard(language)
            )
            return
        
        # Handle contact message
        if user_state == "waiting_contact_message":
            db.add_message(user_id, text)
            db.redis.delete(f"user_state:{user_id}")
            await message.reply_text(
                get_text("message_sent", language),
                reply_markup=main_menu_keyboard(language)
            )
            return
        
        # Handle broadcast message (admin only)
        if user_state == "waiting_broadcast_message" and user_id == ADMIN_ID:
            await self.send_broadcast(user_id, text, message)
            return

        # Handle coupon search (admin only)
        if user_state == "waiting_coupon_code" and user_id == ADMIN_ID:
            await self.search_coupon(user_id, text, message)
            return
        
        # Handle form inputs (admin only)
        if user_id == ADMIN_ID:
            # Check if it's an edit form
            if user_state and user_state.startswith("edit_"):
                await self.handle_edit_form_input(user_id, user_state, text, message)
            else:
                await self.handle_form_input(user_id, user_state, text, message)
    
    async def show_services(self, user_id, message_or_callback, page=0):
        language = db.get_user_language(user_id)
        services = db.get_all_services()
        
        if not services:
            text = get_text("no_services", language)
            keyboard = back_keyboard(language)
        else:
            items_per_page = 5
            total_pages = math.ceil(len(services) / items_per_page)
            start_idx = page * items_per_page
            end_idx = start_idx + items_per_page
            page_services = services[start_idx:end_idx]
            
            text = get_text("available_services", language)
            keyboard = pagination_keyboard(page_services, page, total_pages, "services", language)
        
        if hasattr(message_or_callback, 'edit_message_text'):
            await message_or_callback.edit_message_text(text, reply_markup=keyboard)
        else:
            await message_or_callback.reply_text(text, reply_markup=keyboard)
    
    async def show_channels(self, user_id, message_or_callback, page=0):
        language = db.get_user_language(user_id)
        channels = db.get_all_channels()
        
        if not channels:
            text = get_text("no_channels", language)
            keyboard = back_keyboard(language)
        else:
            items_per_page = 5
            total_pages = math.ceil(len(channels) / items_per_page)
            start_idx = page * items_per_page
            end_idx = start_idx + items_per_page
            page_channels = channels[start_idx:end_idx]
            
            text = get_text("available_channels", language)
            keyboard = pagination_keyboard(page_channels, page, total_pages, "channels", language)
        
        if hasattr(message_or_callback, 'edit_message_text'):
            await message_or_callback.edit_message_text(text, reply_markup=keyboard)
        else:
            await message_or_callback.reply_text(text, reply_markup=keyboard)
    
    async def show_numbers(self, user_id, message_or_callback, page=0):
        language = db.get_user_language(user_id)
        numbers = db.get_all_numbers()

        if not numbers:
            text = get_text("no_numbers", language)
            keyboard = back_keyboard(language)
        else:
            items_per_page = 5
            total_pages = math.ceil(len(numbers) / items_per_page)
            start_idx = page * items_per_page
            end_idx = start_idx + items_per_page
            page_numbers = numbers[start_idx:end_idx]

            text = get_text("available_numbers", language)
            keyboard = pagination_keyboard(page_numbers, page, total_pages, "numbers", language)

        if hasattr(message_or_callback, 'edit_message_text'):
            await message_or_callback.edit_message_text(text, reply_markup=keyboard)
        else:
            await message_or_callback.reply_text(text, reply_markup=keyboard)

    async def show_item_details(self, callback_query: CallbackQuery, data):
        user_id = callback_query.from_user.id
        language = db.get_user_language(user_id)

        parts = data.split("_")
        item_type = parts[0]
        item_id = int(parts[2])

        if item_type == "services":
            item = db.get_service(item_id)
            if item:
                title = item.get("title", {}).get(language, "")
                description = item.get("description", {}).get(language, "")
                price = item.get("price", "")

                text = f"{get_text('service_details', language)}\n\n"
                text += f"📋 {title}\n\n"
                text += f"📝 {description}\n\n"
                text += f"💰 {get_text('price', language)} {price}"

        elif item_type == "channels":
            item = db.get_channel(item_id)
            if item:
                title = item.get("title", {}).get(language, "")
                description = item.get("description", {}).get(language, "")
                price = item.get("price", "")
                subscribers = item.get("subscribers", "")
                category = item.get("category", {}).get(language, "")
                channel_type = get_text("enhanced", language) if item.get("type") == "enhanced" else get_text("regular", language)

                text = f"{get_text('channel_details', language)}\n\n"
                text += f"📺 {title}\n\n"
                text += f"📝 {description}\n\n"
                text += f"👥 {get_text('subscribers', language)} {subscribers}\n"
                text += f"📂 {get_text('category', language)} {category}\n"
                text += f"✨ {channel_type}\n"
                text += f"💰 {get_text('price', language)} {price}"

        elif item_type == "numbers":
            item = db.get_number(item_id)
            if item:
                number = item.get("number", "")
                country = item.get("country", {}).get(language, "")
                operator = item.get("operator", {}).get(language, "")
                price = item.get("price", "")

                text = f"{get_text('number_details', language)}\n\n"
                text += f"📱 {number}\n\n"
                text += f"🌍 {get_text('country', language)} {country}\n"
                text += f"📡 {get_text('operator', language)} {operator}\n"
                text += f"💰 {get_text('price', language)} {price}"

        if item:
            keyboard = item_detail_keyboard(item_id, item_type, language)
            await callback_query.edit_message_text(text, reply_markup=keyboard)
        else:
            await callback_query.answer(get_text("error_occurred", language))

    async def handle_pagination(self, callback_query: CallbackQuery, data):
        parts = data.split("_")
        item_type = parts[0]
        page = int(parts[2])
        user_id = callback_query.from_user.id

        if item_type == "services":
            await self.show_services(user_id, callback_query, page)
        elif item_type == "channels":
            await self.show_channels(user_id, callback_query, page)
        elif item_type == "numbers":
            await self.show_numbers(user_id, callback_query, page)

    async def handle_admin_callback(self, callback_query: CallbackQuery, data):
        user_id = callback_query.from_user.id
        language = db.get_user_language(user_id)

        if data == "admin_add_service":
            await self.start_add_service(user_id, callback_query)
        elif data == "admin_add_channel":
            await self.start_add_channel(user_id, callback_query)
        elif data == "admin_add_number":
            await self.start_add_number(user_id, callback_query)
        elif data == "admin_stats":
            await self.show_stats(user_id, callback_query)
        elif data == "admin_messages":
            await self.show_messages(user_id, callback_query)
        elif data == "admin_broadcast":
            await callback_query.edit_message_text(
                get_text("enter_broadcast_message", language),
                reply_markup=back_keyboard(language)
            )
            db.redis.set(f"user_state:{user_id}", "waiting_broadcast_message")
        elif data == "admin_edit_services":
            await self.show_admin_items(callback_query, "services", "edit")
        elif data == "admin_edit_channels":
            await self.show_admin_items(callback_query, "channels", "edit")
        elif data == "admin_edit_numbers":
            await self.show_admin_items(callback_query, "numbers", "edit")
        elif data == "admin_toggle":
            await self.show_toggle_menu(callback_query)
        elif data == "admin_search_coupon":
            await callback_query.edit_message_text(
                get_text("enter_coupon_code", language),
                reply_markup=back_keyboard(language)
            )
            db.redis.set(f"user_state:{user_id}", "waiting_coupon_code")
        elif data == "admin_pending_coupons":
            await self.show_pending_coupons(callback_query)

    async def show_stats(self, user_id, message_or_callback):
        language = db.get_user_language(user_id)

        users_count = db.get_users_count()
        services_count = len(db.get_all_services())
        channels_count = len(db.get_all_channels())
        numbers_count = len(db.get_all_numbers())
        unread_messages = len(db.get_all_messages("unread"))
        pending_coupons = db.get_pending_coupons_count()

        text = f"📊 {get_text('users_stats', language)}\n\n"
        text += f"👥 {get_text('total_users', language)} {users_count}\n"
        text += f"🔧 {get_text('total_services', language)} {services_count}\n"
        text += f"📺 {get_text('total_channels', language)} {channels_count}\n"
        text += f"📱 {get_text('total_numbers', language)} {numbers_count}\n"
        text += f"📨 {get_text('unread_messages', language)} {unread_messages}\n"
        text += f"🎫 الكوبونات المعلقة: {pending_coupons}"

        keyboard = back_keyboard(language) if user_id != ADMIN_ID else InlineKeyboardMarkup([
            [InlineKeyboardButton(get_text("back", language), callback_data="admin")]
        ])

        if hasattr(message_or_callback, 'edit_message_text'):
            await message_or_callback.edit_message_text(text, reply_markup=keyboard)
        else:
            await message_or_callback.reply_text(text, reply_markup=keyboard)

    async def show_messages(self, user_id, callback_query):
        language = db.get_user_language(user_id)
        messages = db.get_all_messages("unread")

        if not messages:
            text = "لا توجد رسائل جديدة" if language == "ar" else "No new messages" if language == "en" else "پیام جدیدی وجود ندارد"
        else:
            text = f"📨 {get_text('messages', language)} ({len(messages)}):\n\n"
            for msg in messages[:10]:  # Show only first 10 messages
                user_info = f"👤 User ID: {msg['user_id']}\n"
                message_text = f"💬 {msg['message']}\n"
                timestamp = f"🕐 {msg['timestamp']}\n"
                text += f"{user_info}{message_text}{timestamp}\n---\n"

        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton(get_text("back", language), callback_data="admin")]
        ])

        await callback_query.edit_message_text(text, reply_markup=keyboard)

    async def send_broadcast(self, admin_id, message_text, message):
        language = db.get_user_language(admin_id)
        users = db.get_all_users()

        sent_count = 0
        for user_id in users:
            try:
                await self.app.send_message(int(user_id), message_text)
                sent_count += 1
            except:
                continue

        db.redis.delete(f"user_state:{admin_id}")
        await message.reply_text(
            f"{get_text('broadcast_sent', language)}\n📊 تم الإرسال إلى {sent_count} مستخدم",
            reply_markup=admin_main_keyboard(language)
        )

    async def start_add_service(self, user_id, message_or_callback):
        language = db.get_user_language(user_id)
        text = get_text("enter_title_ar", language)

        if hasattr(message_or_callback, 'edit_message_text'):
            await message_or_callback.edit_message_text(text, reply_markup=back_keyboard(language))
        else:
            await message_or_callback.reply_text(text, reply_markup=cancel_keyboard(language))

        db.redis.set(f"user_state:{user_id}", "add_service_title_ar")

    async def start_add_channel(self, user_id, message_or_callback):
        language = db.get_user_language(user_id)
        text = get_text("enter_title_ar", language)

        if hasattr(message_or_callback, 'edit_message_text'):
            await message_or_callback.edit_message_text(text, reply_markup=back_keyboard(language))
        else:
            await message_or_callback.reply_text(text, reply_markup=cancel_keyboard(language))

        db.redis.set(f"user_state:{user_id}", "add_channel_title_ar")

    async def start_add_number(self, user_id, message_or_callback):
        language = db.get_user_language(user_id)
        text = get_text("enter_number", language)

        if hasattr(message_or_callback, 'edit_message_text'):
            await message_or_callback.edit_message_text(text, reply_markup=back_keyboard(language))
        else:
            await message_or_callback.reply_text(text, reply_markup=cancel_keyboard(language))

        db.redis.set(f"user_state:{user_id}", "add_number_number")

    async def handle_form_input(self, user_id, state, text, message):
        language = db.get_user_language(user_id)

        # Get or create form data
        form_data_key = f"form_data:{user_id}"
        form_data = db.redis.get(form_data_key)
        if form_data:
            import json
            form_data = json.loads(form_data)
        else:
            form_data = {}

        # Handle service form
        if state.startswith("add_service_"):
            await self.handle_service_form(user_id, state, text, message, form_data, form_data_key)

        # Handle channel form
        elif state.startswith("add_channel_"):
            await self.handle_channel_form(user_id, state, text, message, form_data, form_data_key)

        # Handle number form
        elif state.startswith("add_number_"):
            await self.handle_number_form(user_id, state, text, message, form_data, form_data_key)

    async def handle_service_form(self, user_id, state, text, message, form_data, form_data_key):
        language = db.get_user_language(user_id)

        if state == "add_service_title_ar":
            form_data["title_ar"] = text
            db.redis.set(form_data_key, json.dumps(form_data))
            db.redis.set(f"user_state:{user_id}", "add_service_description_ar")
            await message.reply_text(get_text("enter_description_ar", language), reply_markup=cancel_keyboard(language))

        elif state == "add_service_description_ar":
            form_data["description_ar"] = text
            db.redis.set(form_data_key, json.dumps(form_data))
            db.redis.set(f"user_state:{user_id}", "add_service_price")
            await message.reply_text(get_text("enter_price", language), reply_markup=cancel_keyboard(language))

        elif state == "add_service_price":
            form_data["price"] = text

            # Show translation message
            await message.reply_text(get_text("auto_translating", language))

            # Auto translate content
            translations = translator.translate_service_data(
                form_data["title_ar"],
                form_data["description_ar"]
            )

            # Create service with auto-translated content
            service_data = {
                "title": translations["title"],
                "description": translations["description"],
                "price": form_data["price"]
            }

            db.add_service(service_data)

            # Clean up
            db.redis.delete(f"user_state:{user_id}")
            db.redis.delete(form_data_key)

            await message.reply_text(
                f"{get_text('translation_completed', language)}\n{get_text('service_added', language)}",
                reply_markup=admin_main_keyboard(language)
            )

    async def handle_channel_form(self, user_id, state, text, message, form_data, form_data_key):
        language = db.get_user_language(user_id)

        if state == "add_channel_title_ar":
            form_data["title_ar"] = text
            db.redis.set(form_data_key, json.dumps(form_data))
            db.redis.set(f"user_state:{user_id}", "add_channel_description_ar")
            await message.reply_text(get_text("enter_description_ar", language), reply_markup=cancel_keyboard(language))

        elif state == "add_channel_description_ar":
            form_data["description_ar"] = text
            db.redis.set(form_data_key, json.dumps(form_data))
            db.redis.set(f"user_state:{user_id}", "add_channel_subscribers")
            await message.reply_text(get_text("enter_subscribers", language), reply_markup=cancel_keyboard(language))

        elif state == "add_channel_subscribers":
            form_data["subscribers"] = text
            db.redis.set(form_data_key, json.dumps(form_data))
            db.redis.set(f"user_state:{user_id}", "add_channel_category")
            await message.reply_text(get_text("enter_category", language), reply_markup=cancel_keyboard(language))

        elif state == "add_channel_category":
            form_data["category"] = text
            db.redis.set(form_data_key, json.dumps(form_data))
            db.redis.set(f"user_state:{user_id}", "add_channel_price")
            await message.reply_text(get_text("enter_price", language), reply_markup=cancel_keyboard(language))

        elif state == "add_channel_price":
            form_data["price"] = text

            # Show translation message
            await message.reply_text(get_text("auto_translating", language))

            # Auto translate content
            translations = translator.translate_channel_data(
                form_data["title_ar"],
                form_data["description_ar"],
                form_data["category"]
            )

            # Create channel with auto-translated content
            channel_data = {
                "title": translations["title"],
                "description": translations["description"],
                "subscribers": form_data["subscribers"],
                "category": translations["category"],
                "price": form_data["price"],
                "type": "regular"  # Default type
            }

            db.add_channel(channel_data)

            # Clean up
            db.redis.delete(f"user_state:{user_id}")
            db.redis.delete(form_data_key)

            await message.reply_text(
                f"{get_text('translation_completed', language)}\n{get_text('channel_added', language)}",
                reply_markup=admin_main_keyboard(language)
            )

    async def handle_number_form(self, user_id, state, text, message, form_data, form_data_key):
        language = db.get_user_language(user_id)

        if state == "add_number_number":
            form_data["number"] = text
            db.redis.set(form_data_key, json.dumps(form_data))
            db.redis.set(f"user_state:{user_id}", "add_number_country")
            await message.reply_text(get_text("enter_country", language), reply_markup=cancel_keyboard(language))

        elif state == "add_number_country":
            form_data["country"] = text
            db.redis.set(form_data_key, json.dumps(form_data))
            db.redis.set(f"user_state:{user_id}", "add_number_operator")
            await message.reply_text(get_text("enter_operator", language), reply_markup=cancel_keyboard(language))

        elif state == "add_number_operator":
            form_data["operator"] = text
            db.redis.set(form_data_key, json.dumps(form_data))
            db.redis.set(f"user_state:{user_id}", "add_number_price")
            await message.reply_text(get_text("enter_price", language), reply_markup=cancel_keyboard(language))

        elif state == "add_number_price":
            form_data["price"] = text

            # Show translation message
            await message.reply_text(get_text("auto_translating", language))

            # Auto translate content
            translations = translator.translate_number_data(
                form_data["country"],
                form_data["operator"]
            )

            # Create number with auto-translated content
            number_data = {
                "number": form_data["number"],
                "country": translations["country"],
                "operator": translations["operator"],
                "price": form_data["price"]
            }

            db.add_number(number_data)

            # Clean up
            db.redis.delete(f"user_state:{user_id}")
            db.redis.delete(form_data_key)

            await message.reply_text(
                f"{get_text('translation_completed', language)}\n{get_text('number_added', language)}",
                reply_markup=admin_main_keyboard(language)
            )

    async def show_admin_items(self, callback_query, item_type, action):
        user_id = callback_query.from_user.id
        language = db.get_user_language(user_id)

        if item_type == "services":
            items = db.get_all_services("active") + db.get_all_services("inactive")
        elif item_type == "channels":
            items = db.get_all_channels("active") + db.get_all_channels("inactive")
        elif item_type == "numbers":
            items = db.get_all_numbers("active") + db.get_all_numbers("inactive")

        if not items:
            text = f"لا توجد {item_type} متوفرة" if language == "ar" else f"No {item_type} available" if language == "en" else f"{item_type} موجود نیست"
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton(get_text("back", language), callback_data="admin")]
            ])
        else:
            text = f"اختر {item_type} للتعديل:" if language == "ar" else f"Choose {item_type} to edit:" if language == "en" else f"{item_type} را برای ویرایش انتخاب کنید:"
            keyboard = admin_items_keyboard(items, item_type, action, language)

        await callback_query.edit_message_text(text, reply_markup=keyboard)

    async def show_toggle_menu(self, callback_query):
        user_id = callback_query.from_user.id
        language = db.get_user_language(user_id)

        keyboard = [
            [
                InlineKeyboardButton("🔧 خدمات" if language == "ar" else "🔧 Services" if language == "en" else "🔧 خدمات",
                                   callback_data="admin_toggle_services"),
                InlineKeyboardButton("📺 قنوات" if language == "ar" else "📺 Channels" if language == "en" else "📺 کانال‌ها",
                                   callback_data="admin_toggle_channels")
            ],
            [
                InlineKeyboardButton("📱 أرقام" if language == "ar" else "📱 Numbers" if language == "en" else "📱 شماره‌ها",
                                   callback_data="admin_toggle_numbers")
            ],
            [InlineKeyboardButton(get_text("back", language), callback_data="admin")]
        ]

        text = "اختر النوع لتفعيل/تعطيل العناصر:" if language == "ar" else "Choose type to toggle items:" if language == "en" else "نوع را برای تغییر وضعیت انتخاب کنید:"
        await callback_query.edit_message_text(text, reply_markup=InlineKeyboardMarkup(keyboard))

    async def handle_edit_form_input(self, user_id, state, text, message):
        """Handle edit form inputs"""
        language = db.get_user_language(user_id)

        # Get edit context and data
        edit_context = db.redis.get(f"edit_context:{user_id}")
        edit_data = db.redis.get(f"edit_data:{user_id}")

        if not edit_context or not edit_data:
            await message.reply_text(get_text("error_occurred", language))
            return

        edit_context = json.loads(edit_context)
        edit_data = json.loads(edit_data)
        item_type = edit_context["item_type"]
        item_id = edit_context["item_id"]

        # Handle service/channel edit
        if item_type in ["service", "channel"]:
            if state == f"edit_{item_type}_title_ar":
                edit_data["title"]["ar"] = text
                db.redis.set(f"edit_data:{user_id}", json.dumps(edit_data))
                db.redis.set(f"user_state:{user_id}", f"edit_{item_type}_title_en")
                await message.reply_text(get_text("enter_title_en", language))

            elif state == f"edit_{item_type}_title_en":
                edit_data["title"]["en"] = text
                db.redis.set(f"edit_data:{user_id}", json.dumps(edit_data))
                db.redis.set(f"user_state:{user_id}", f"edit_{item_type}_title_fa")
                await message.reply_text(get_text("enter_title_fa", language))

            elif state == f"edit_{item_type}_title_fa":
                edit_data["title"]["fa"] = text
                db.redis.set(f"edit_data:{user_id}", json.dumps(edit_data))
                db.redis.set(f"user_state:{user_id}", f"edit_{item_type}_description_ar")
                await message.reply_text(get_text("enter_description_ar", language))

            elif state == f"edit_{item_type}_description_ar":
                edit_data["description"]["ar"] = text
                db.redis.set(f"edit_data:{user_id}", json.dumps(edit_data))
                db.redis.set(f"user_state:{user_id}", f"edit_{item_type}_description_en")
                await message.reply_text(get_text("enter_description_en", language))

            elif state == f"edit_{item_type}_description_en":
                edit_data["description"]["en"] = text
                db.redis.set(f"edit_data:{user_id}", json.dumps(edit_data))
                db.redis.set(f"user_state:{user_id}", f"edit_{item_type}_description_fa")
                await message.reply_text(get_text("enter_description_fa", language))

            elif state == f"edit_{item_type}_description_fa":
                edit_data["description"]["fa"] = text
                db.redis.set(f"edit_data:{user_id}", json.dumps(edit_data))

                if item_type == "channel":
                    db.redis.set(f"user_state:{user_id}", f"edit_channel_subscribers")
                    await message.reply_text(get_text("enter_subscribers", language))
                else:
                    db.redis.set(f"user_state:{user_id}", f"edit_{item_type}_price")
                    await message.reply_text(get_text("enter_price", language))

            elif state == "edit_channel_subscribers":
                edit_data["subscribers"] = text
                db.redis.set(f"edit_data:{user_id}", json.dumps(edit_data))
                db.redis.set(f"user_state:{user_id}", "edit_channel_category")
                await message.reply_text(get_text("enter_category", language))

            elif state == "edit_channel_category":
                edit_data["category"] = {"ar": text, "en": text, "fa": text}
                db.redis.set(f"edit_data:{user_id}", json.dumps(edit_data))
                db.redis.set(f"user_state:{user_id}", "edit_channel_price")
                await message.reply_text(get_text("enter_price", language))

            elif state == f"edit_{item_type}_price":
                edit_data["price"] = text

                # Update item
                if item_type == "service":
                    db.update_service(item_id, edit_data)
                    success_msg = get_text("service_updated", language)
                elif item_type == "channel":
                    db.update_channel(item_id, edit_data)
                    success_msg = get_text("channel_updated", language)

                # Clean up
                db.redis.delete(f"user_state:{user_id}")
                db.redis.delete(f"edit_context:{user_id}")
                db.redis.delete(f"edit_data:{user_id}")

                await message.reply_text(success_msg, reply_markup=admin_main_keyboard(language))

        # Handle number edit
        elif item_type == "number":
            if state == "edit_number_number":
                edit_data["number"] = text
                db.redis.set(f"edit_data:{user_id}", json.dumps(edit_data))
                db.redis.set(f"user_state:{user_id}", "edit_number_country")
                await message.reply_text(get_text("enter_country", language))

            elif state == "edit_number_country":
                edit_data["country"] = {"ar": text, "en": text, "fa": text}
                db.redis.set(f"edit_data:{user_id}", json.dumps(edit_data))
                db.redis.set(f"user_state:{user_id}", "edit_number_operator")
                await message.reply_text(get_text("enter_operator", language))

            elif state == "edit_number_operator":
                edit_data["operator"] = {"ar": text, "en": text, "fa": text}
                db.redis.set(f"edit_data:{user_id}", json.dumps(edit_data))
                db.redis.set(f"user_state:{user_id}", "edit_number_price")
                await message.reply_text(get_text("enter_price", language))

            elif state == "edit_number_price":
                edit_data["price"] = text

                # Update number
                db.update_number(item_id, edit_data)

                # Clean up
                db.redis.delete(f"user_state:{user_id}")
                db.redis.delete(f"edit_context:{user_id}")
                db.redis.delete(f"edit_data:{user_id}")

                await message.reply_text(
                    get_text("number_updated", language),
                    reply_markup=admin_main_keyboard(language)
                )

    async def handle_buy_callback(self, callback_query, data):
        """Handle buy button callbacks"""
        user_id = callback_query.from_user.id
        language = db.get_user_language(user_id)

        try:
            parts = data.split("_")

            # Handle different callback formats
            if data.startswith("buy_"):
                # Format: buy_service_1, buy_channel_1, buy_number_1
                item_type = parts[1]
                item_id = int(parts[2])
            else:
                # Format: services_buy_1, channels_buy_1, numbers_buy_1
                item_type = parts[0].rstrip('s')  # Remove 's' from services/channels/numbers
                item_id = int(parts[2])

            # Initialize variables
            item = None
            item_title = ""
            price = ""

            # Get item details
            if item_type == "service":
                item = db.get_service(item_id)
                if item:
                    item_title = item.get("title", {}).get(language, "")
                    price = item.get("price", "")
            elif item_type == "channel":
                item = db.get_channel(item_id)
                if item:
                    item_title = item.get("title", {}).get(language, "")
                    price = item.get("price", "")
            elif item_type == "number":
                item = db.get_number(item_id)
                if item:
                    item_title = item.get("number", "")
                    price = item.get("price", "")

            if not item:
                await callback_query.answer(get_text("error_occurred", language))
                return

            # Create coupon
            coupon_code = db.create_coupon(user_id, item_type, item_id, item_title, price)

            # Send coupon to user
            coupon_text = f"{get_text('coupon_created', language)}\n\n"
            coupon_text += f"🎫 {get_text('coupon_code', language)} {coupon_code}\n"
            coupon_text += f"📦 {item_title}\n"
            coupon_text += f"💰 {get_text('price', language)} {price}\n\n"
            coupon_text += f"📞 {get_text('coupon_instructions', language)}"

            await callback_query.edit_message_text(
                coupon_text,
                reply_markup=back_keyboard(language)
            )

            # Notify admin about new coupon
            try:
                from config import ADMIN_ID
                admin_language = db.get_user_language(ADMIN_ID)
                admin_text = f"🎫 كوبون جديد!\n\n"
                admin_text += f"👤 المستخدم: {user_id}\n"
                admin_text += f"🎫 الكود: {coupon_code}\n"
                admin_text += f"📦 المنتج: {item_title}\n"
                admin_text += f"💰 السعر: {price}"

                await self.app.send_message(
                    ADMIN_ID,
                    admin_text,
                    reply_markup=coupon_keyboard(coupon_code, admin_language)
                )
            except Exception as e:
                print(f"Error notifying admin: {e}")

        except Exception as e:
            print(f"Error in buy callback: {e}")
            await callback_query.answer(get_text("error_occurred", language))

    async def search_coupon(self, user_id, coupon_code, message):
        """Search for a coupon by code"""
        language = db.get_user_language(user_id)

        coupon = db.get_coupon(coupon_code.strip())

        if not coupon:
            await message.reply_text(
                get_text("coupon_not_found", language),
                reply_markup=admin_main_keyboard(language)
            )
        else:
            # Display coupon details
            status_text = get_text(f"coupon_{coupon['status']}", language)

            coupon_text = f"{get_text('coupon_details', language)}\n\n"
            coupon_text += f"🎫 {get_text('coupon_code', language)} {coupon['code']}\n"
            coupon_text += f"👤 المستخدم: {coupon['user_id']}\n"
            coupon_text += f"📦 المنتج: {coupon['item_title']}\n"
            coupon_text += f"💰 السعر: {coupon['price']}\n"
            coupon_text += f"📊 {get_text('coupon_status', language)} {status_text}\n"
            coupon_text += f"📅 تاريخ الإنشاء: {coupon['created_at']}"

            keyboard = coupon_keyboard(coupon_code, language) if coupon['status'] == 'pending' else back_keyboard(language)

            await message.reply_text(coupon_text, reply_markup=keyboard)

        # Clean up state
        db.redis.delete(f"user_state:{user_id}")

    async def show_pending_coupons(self, callback_query):
        """Show all pending coupons"""
        user_id = callback_query.from_user.id
        language = db.get_user_language(user_id)

        pending_coupons = db.get_all_coupons("pending")

        if not pending_coupons:
            text = "لا توجد كوبونات معلقة" if language == "ar" else "No pending coupons" if language == "en" else "کوپن معلقی وجود ندارد"
        else:
            text = f"{get_text('pending_coupons', language)} ({len(pending_coupons)})\n\n"
            for coupon in pending_coupons[:10]:  # Show first 10
                text += f"🎫 {coupon['code']} - {coupon['item_title']} - {coupon['price']}\n"
                text += f"👤 المستخدم: {coupon['user_id']}\n\n"

        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton(get_text("back", language), callback_data="admin")]
        ])

        await callback_query.edit_message_text(text, reply_markup=keyboard)

    async def complete_coupon(self, callback_query, coupon_code):
        """Complete a coupon (mark as paid)"""
        user_id = callback_query.from_user.id
        language = db.get_user_language(user_id)

        coupon = db.get_coupon(coupon_code)
        if coupon:
            db.update_coupon_status(coupon_code, "completed")

            # Notify user
            try:
                user_language = db.get_user_language(int(coupon['user_id']))
                await self.app.send_message(
                    int(coupon['user_id']),
                    f"✅ تم تأكيد كوبونك {coupon_code} وإتمام عملية الشراء!"
                )
            except:
                pass

            await callback_query.edit_message_text(
                get_text("coupon_completed_success", language),
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton(get_text("back", language), callback_data="admin")]
                ])
            )
        else:
            await callback_query.answer(get_text("coupon_not_found", language))

    async def cancel_coupon(self, callback_query, coupon_code):
        """Cancel a coupon"""
        user_id = callback_query.from_user.id
        language = db.get_user_language(user_id)

        coupon = db.get_coupon(coupon_code)
        if coupon:
            db.update_coupon_status(coupon_code, "cancelled")

            # Notify user
            try:
                user_language = db.get_user_language(int(coupon['user_id']))
                await self.app.send_message(
                    int(coupon['user_id']),
                    f"❌ تم إلغاء كوبونك {coupon_code}"
                )
            except:
                pass

            await callback_query.edit_message_text(
                get_text("coupon_cancelled_success", language),
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton(get_text("back", language), callback_data="admin")]
                ])
            )
        else:
            await callback_query.answer(get_text("coupon_not_found", language))

    async def handle_admin_action_callback(self, callback_query, data):
        """Handle admin action callbacks (edit, delete, toggle)"""
        from admin_handlers import AdminHandlers

        admin_handler = AdminHandlers(self)
        user_id = callback_query.from_user.id
        language = db.get_user_language(user_id)

        try:
            if data.startswith("admin_edit_service_") or data.startswith("admin_edit_channel_") or data.startswith("admin_edit_number_"):
                await admin_handler.handle_admin_edit_callback(callback_query, data)

            elif data.startswith("admin_start_edit_"):
                parts = data.split("_")
                item_type = parts[3]
                item_id = int(parts[4])
                await admin_handler.start_edit_item(callback_query, item_type, item_id)

            elif data.startswith("admin_confirm_delete_"):
                parts = data.split("_")
                item_type = parts[3]
                item_id = int(parts[4])
                await admin_handler.confirm_delete(callback_query, item_type, item_id)

            elif data.startswith("admin_delete_confirmed_"):
                parts = data.split("_")
                item_type = parts[3]
                item_id = int(parts[4])
                await admin_handler.delete_item(callback_query, item_type, item_id)

            elif data.startswith("admin_toggle_item_"):
                parts = data.split("_")
                item_type = parts[3]
                item_id = int(parts[4])
                await admin_handler.toggle_item_status(callback_query, item_type, item_id)

            elif data.startswith("admin_toggle_"):
                await admin_handler.handle_toggle_callback(callback_query, data)

            elif data.startswith("complete_coupon_"):
                coupon_code = data.split("_", 2)[2]
                await self.complete_coupon(callback_query, coupon_code)

            elif data.startswith("cancel_coupon_"):
                coupon_code = data.split("_", 2)[2]
                await self.cancel_coupon(callback_query, coupon_code)

        except Exception as e:
            print(f"Error in admin action callback: {e}")
            await callback_query.answer(get_text("error_occurred", language))
