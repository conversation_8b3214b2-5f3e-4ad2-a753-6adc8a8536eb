#!/usr/bin/env python3
"""
Installation script for Market Bot
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Packages installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing packages: {e}")
        return False
    return True

def setup_env_file():
    """Setup environment file"""
    if os.path.exists(".env"):
        print("⚠️  .env file already exists")
        return True
    
    print("🔧 Setting up environment file...")
    
    # Get user input
    api_id = input("Enter your API_ID: ")
    api_hash = input("Enter your API_HASH: ")
    bot_token = input("Enter your BOT_TOKEN: ")
    admin_id = input("Enter your ADMIN_ID (your Telegram user ID): ")
    
    # Create .env file
    env_content = f"""API_ID={api_id}
API_HASH={api_hash}
BOT_TOKEN={bot_token}
ADMIN_ID={admin_id}
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
"""
    
    try:
        with open(".env", "w", encoding="utf-8") as f:
            f.write(env_content)
        print("✅ Environment file created successfully!")
        return True
    except Exception as e:
        print(f"❌ Error creating .env file: {e}")
        return False

def check_redis():
    """Check if Redis is available"""
    print("🔍 Checking Redis connection...")
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis is running!")
        return True
    except Exception as e:
        print(f"⚠️  Redis connection failed: {e}")
        print("📝 Please make sure Redis is installed and running")
        print("   - Windows: Download from https://github.com/microsoftarchive/redis/releases")
        print("   - Linux: sudo apt-get install redis-server")
        print("   - macOS: brew install redis")
        return False

def main():
    print("🚀 Market Bot Installation")
    print("=" * 30)
    
    # Install requirements
    if not install_requirements():
        return
    
    # Setup environment
    if not setup_env_file():
        return
    
    # Check Redis
    check_redis()
    
    print("\n🎉 Installation completed!")
    print("📋 Next steps:")
    print("   1. Make sure Redis is running")
    print("   2. Update .env file with your credentials")
    print("   3. Run: python run.py")

if __name__ == "__main__":
    main()
