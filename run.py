#!/usr/bin/env python3
"""
Market Bot - Telegram Bot for selling channels, numbers and services
Supports Arabic, English and Persian languages
"""

import asyncio
import logging
from main import main

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
    except Exception as e:
        print(f"❌ Error starting bot: {e}")
