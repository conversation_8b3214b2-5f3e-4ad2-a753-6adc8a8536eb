import os
from dotenv import load_dotenv

load_dotenv()

API_ID = int(os.getenv("API_ID"))
API_HASH = os.getenv("API_HASH")
BOT_TOKEN = os.getenv("BOT_TOKEN")
ADMIN_ID = int(os.getenv("ADMIN_ID"))
REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
REDIS_PORT = int(os.getenv("REDIS_PORT", 6379))
REDIS_DB = int(os.getenv("REDIS_DB", 0))

# Languages
LANGUAGES = {
    "ar": "🇸🇦 العربية",
    "en": "🇬🇧 English", 
    "fa": "🇮🇷 فارسی"
}

# Default language
DEFAULT_LANGUAGE = "ar"
