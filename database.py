import redis
import json
from config import REDIS_HOST, REDIS_PORT, REDIS_DB

class Database:
    def __init__(self):
        self.redis = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB, decode_responses=True)
    
    # User management
    def add_user(self, user_id, language="ar"):
        user_data = {
            "user_id": user_id,
            "language": language,
            "joined_at": str(int(__import__("time").time()))
        }
        self.redis.hset(f"user:{user_id}", mapping=user_data)
        self.redis.sadd("users", user_id)
    
    def get_user(self, user_id):
        user_data = self.redis.hgetall(f"user:{user_id}")
        return user_data if user_data else None
    
    def set_user_language(self, user_id, language):
        self.redis.hset(f"user:{user_id}", "language", language)
    
    def get_user_language(self, user_id):
        user = self.get_user(user_id)
        return user.get("language", "ar") if user else "ar"
    
    def get_all_users(self):
        return list(self.redis.smembers("users"))
    
    def get_users_count(self):
        return self.redis.scard("users")
    
    # Services management
    def add_service(self, service_data):
        service_id = self.redis.incr("service_counter")
        service_data["id"] = service_id
        service_data["status"] = "active"
        self.redis.hset(f"service:{service_id}", mapping=self._serialize_dict(service_data))
        self.redis.sadd("services", service_id)
        return service_id
    
    def get_service(self, service_id):
        service_data = self.redis.hgetall(f"service:{service_id}")
        return self._deserialize_dict(service_data) if service_data else None
    
    def get_all_services(self, status="active"):
        services = []
        for service_id in self.redis.smembers("services"):
            service = self.get_service(service_id)
            if service and service.get("status") == status:
                services.append(service)
        return services
    
    def update_service(self, service_id, service_data):
        self.redis.hset(f"service:{service_id}", mapping=self._serialize_dict(service_data))
    
    def delete_service(self, service_id):
        self.redis.delete(f"service:{service_id}")
        self.redis.srem("services", service_id)
    
    def toggle_service_status(self, service_id):
        service = self.get_service(service_id)
        if service:
            new_status = "inactive" if service.get("status") == "active" else "active"
            self.redis.hset(f"service:{service_id}", "status", new_status)
            return new_status
        return None
    
    # Channels management
    def add_channel(self, channel_data):
        channel_id = self.redis.incr("channel_counter")
        channel_data["id"] = channel_id
        channel_data["status"] = "active"
        self.redis.hset(f"channel:{channel_id}", mapping=self._serialize_dict(channel_data))
        self.redis.sadd("channels", channel_id)
        return channel_id
    
    def get_channel(self, channel_id):
        channel_data = self.redis.hgetall(f"channel:{channel_id}")
        return self._deserialize_dict(channel_data) if channel_data else None
    
    def get_all_channels(self, status="active"):
        channels = []
        for channel_id in self.redis.smembers("channels"):
            channel = self.get_channel(channel_id)
            if channel and channel.get("status") == status:
                channels.append(channel)
        return channels
    
    def update_channel(self, channel_id, channel_data):
        self.redis.hset(f"channel:{channel_id}", mapping=self._serialize_dict(channel_data))
    
    def delete_channel(self, channel_id):
        self.redis.delete(f"channel:{channel_id}")
        self.redis.srem("channels", channel_id)
    
    def toggle_channel_status(self, channel_id):
        channel = self.get_channel(channel_id)
        if channel:
            new_status = "inactive" if channel.get("status") == "active" else "active"
            self.redis.hset(f"channel:{channel_id}", "status", new_status)
            return new_status
        return None
    
    # Numbers management
    def add_number(self, number_data):
        number_id = self.redis.incr("number_counter")
        number_data["id"] = number_id
        number_data["status"] = "active"
        self.redis.hset(f"number:{number_id}", mapping=self._serialize_dict(number_data))
        self.redis.sadd("numbers", number_id)
        return number_id
    
    def get_number(self, number_id):
        number_data = self.redis.hgetall(f"number:{number_id}")
        return self._deserialize_dict(number_data) if number_data else None
    
    def get_all_numbers(self, status="active"):
        numbers = []
        for number_id in self.redis.smembers("numbers"):
            number = self.get_number(number_id)
            if number and number.get("status") == status:
                numbers.append(number)
        return numbers
    
    def update_number(self, number_id, number_data):
        self.redis.hset(f"number:{number_id}", mapping=self._serialize_dict(number_data))
    
    def delete_number(self, number_id):
        self.redis.delete(f"number:{number_id}")
        self.redis.srem("numbers", number_id)
    
    def toggle_number_status(self, number_id):
        number = self.get_number(number_id)
        if number:
            new_status = "inactive" if number.get("status") == "active" else "active"
            self.redis.hset(f"number:{number_id}", "status", new_status)
            return new_status
        return None
    
    # Messages management
    def add_message(self, user_id, message):
        message_id = self.redis.incr("message_counter")
        message_data = {
            "id": message_id,
            "user_id": user_id,
            "message": message,
            "timestamp": str(int(__import__("time").time())),
            "status": "unread"
        }
        self.redis.hset(f"message:{message_id}", mapping=message_data)
        self.redis.sadd("messages", message_id)
        return message_id
    
    def get_all_messages(self, status="unread"):
        messages = []
        for message_id in self.redis.smembers("messages"):
            message_data = self.redis.hgetall(f"message:{message_id}")
            if message_data and message_data.get("status") == status:
                messages.append(message_data)
        return messages
    
    def mark_message_read(self, message_id):
        self.redis.hset(f"message:{message_id}", "status", "read")

    # Coupons management
    def create_coupon(self, user_id, item_type, item_id, item_title, price):
        """Create a new purchase coupon"""
        coupon_id = self.redis.incr("coupon_counter")
        coupon_code = f"CP{coupon_id:06d}"

        coupon_data = {
            "id": coupon_id,
            "code": coupon_code,
            "user_id": user_id,
            "item_type": item_type,
            "item_id": item_id,
            "item_title": item_title,
            "price": price,
            "status": "pending",
            "created_at": str(int(__import__("time").time()))
        }

        self.redis.hset(f"coupon:{coupon_code}", mapping=coupon_data)
        self.redis.sadd("coupons", coupon_code)
        self.redis.sadd(f"user_coupons:{user_id}", coupon_code)

        return coupon_code

    def get_coupon(self, coupon_code):
        """Get coupon by code"""
        coupon_data = self.redis.hgetall(f"coupon:{coupon_code}")
        return coupon_data if coupon_data else None

    def get_all_coupons(self, status=None):
        """Get all coupons, optionally filtered by status"""
        coupons = []
        for coupon_code in self.redis.smembers("coupons"):
            coupon_data = self.redis.hgetall(f"coupon:{coupon_code}")
            if coupon_data and (not status or coupon_data.get("status") == status):
                coupons.append(coupon_data)
        return coupons

    def get_user_coupons(self, user_id, status=None):
        """Get user's coupons"""
        coupons = []
        for coupon_code in self.redis.smembers(f"user_coupons:{user_id}"):
            coupon_data = self.redis.hgetall(f"coupon:{coupon_code}")
            if coupon_data and (not status or coupon_data.get("status") == status):
                coupons.append(coupon_data)
        return coupons

    def update_coupon_status(self, coupon_code, status):
        """Update coupon status"""
        self.redis.hset(f"coupon:{coupon_code}", "status", status)

    def get_pending_coupons_count(self):
        """Get count of pending coupons"""
        return len(self.get_all_coupons("pending"))
    
    # Helper methods
    def _serialize_dict(self, data):
        serialized = {}
        for key, value in data.items():
            if isinstance(value, (dict, list)):
                serialized[key] = json.dumps(value, ensure_ascii=False)
            else:
                serialized[key] = str(value)
        return serialized
    
    def _deserialize_dict(self, data):
        deserialized = {}
        for key, value in data.items():
            try:
                deserialized[key] = json.loads(value)
            except (json.JSONDecodeError, TypeError):
                deserialized[key] = value
        return deserialized

db = Database()
