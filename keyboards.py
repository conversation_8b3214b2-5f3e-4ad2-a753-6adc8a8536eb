from pyrogram.types import InlineKeyboardMarkup, InlineKeyboardButton, ReplyKeyboardMarkup, KeyboardButton
from translations import get_text
from config import LANGUAGES

def language_keyboard():
    """Keyboard for language selection"""
    keyboard = []
    for code, name in LANGUAGES.items():
        keyboard.append([InlineKeyboardButton(name, callback_data=f"lang_{code}")])
    return InlineKeyboardMarkup(keyboard)

def main_menu_keyboard(language="ar"):
    """Main menu keyboard"""
    keyboard = [
        [
            InlineKeyboardButton(get_text("services", language), callback_data="services"),
            InlineKeyboardButton(get_text("channels", language), callback_data="channels")
        ],
        [
            InlineKeyboardButton(get_text("numbers", language), callback_data="numbers"),
            InlineKeyboardButton(get_text("contact", language), callback_data="contact")
        ],
        [
            InlineKeyboardButton(get_text("settings", language), callback_data="settings"),
            InlineKeyboardButton(get_text("refresh", language), callback_data="refresh")
        ]
    ]
    return Inline<PERSON>eyboardMarkup(keyboard)

def settings_keyboard(language="ar"):
    """Settings keyboard"""
    keyboard = [
        [InlineKeyboardButton(get_text("change_language", language), callback_data="change_language")],
        [InlineKeyboardButton(get_text("back", language), callback_data="main_menu")]
    ]
    return InlineKeyboardMarkup(keyboard)

def back_keyboard(language="ar"):
    """Simple back button"""
    return InlineKeyboardMarkup([
        [InlineKeyboardButton(get_text("back", language), callback_data="main_menu")]
    ])

def pagination_keyboard(items, current_page, total_pages, item_type, language="ar"):
    """Pagination keyboard for items"""
    keyboard = []
    
    # Navigation buttons
    nav_buttons = []
    if current_page > 0:
        nav_buttons.append(InlineKeyboardButton(get_text("previous", language), callback_data=f"{item_type}_page_{current_page-1}"))
    if current_page < total_pages - 1:
        nav_buttons.append(InlineKeyboardButton(get_text("next", language), callback_data=f"{item_type}_page_{current_page+1}"))
    
    if nav_buttons:
        keyboard.append(nav_buttons)
    
    # Item buttons
    for item in items:
        item_id = item.get("id")
        if item_type == "services":
            title = item.get("title", {}).get(language, "")
        elif item_type == "channels":
            title = item.get("title", {}).get(language, "")
        elif item_type == "numbers":
            title = item.get("number", "")
        
        keyboard.append([InlineKeyboardButton(title, callback_data=f"{item_type}_view_{item_id}")])
    
    # Back button
    keyboard.append([InlineKeyboardButton(get_text("back", language), callback_data="main_menu")])
    
    return InlineKeyboardMarkup(keyboard)

def item_detail_keyboard(item_id, item_type, language="ar"):
    """Keyboard for item details"""
    keyboard = [
        [
            InlineKeyboardButton(get_text("buy_now", language), callback_data=f"buy_{item_type}_{item_id}"),
            InlineKeyboardButton(get_text("contact_us", language), callback_data="contact")
        ],
        [InlineKeyboardButton(get_text("back", language), callback_data=item_type)]
    ]
    return InlineKeyboardMarkup(keyboard)

def admin_main_keyboard(language="ar"):
    """Admin main menu keyboard"""
    keyboard = [
        [
            InlineKeyboardButton(get_text("add_service", language), callback_data="admin_add_service"),
            InlineKeyboardButton(get_text("add_channel", language), callback_data="admin_add_channel")
        ],
        [
            InlineKeyboardButton(get_text("add_number", language), callback_data="admin_add_number"),
            InlineKeyboardButton(get_text("users_stats", language), callback_data="admin_stats")
        ],
        [
            InlineKeyboardButton(get_text("messages", language), callback_data="admin_messages"),
            InlineKeyboardButton(get_text("broadcast", language), callback_data="admin_broadcast")
        ],
        [
            InlineKeyboardButton(get_text("edit_service", language), callback_data="admin_edit_services"),
            InlineKeyboardButton(get_text("edit_channel", language), callback_data="admin_edit_channels")
        ],
        [
            InlineKeyboardButton(get_text("edit_number", language), callback_data="admin_edit_numbers"),
            InlineKeyboardButton(get_text("toggle_status", language), callback_data="admin_toggle")
        ],
        [
            InlineKeyboardButton(get_text("search_coupon", language), callback_data="admin_search_coupon"),
            InlineKeyboardButton("📋 كوبونات معلقة" if language == "ar" else "📋 Pending Coupons" if language == "en" else "📋 كوپن‌های معلق", callback_data="admin_pending_coupons")
        ],
        [InlineKeyboardButton(get_text("back", language), callback_data="main_menu")]
    ]
    return InlineKeyboardMarkup(keyboard)

def admin_items_keyboard(items, item_type, action, language="ar"):
    """Admin keyboard for managing items"""
    keyboard = []
    
    for item in items:
        item_id = item.get("id")
        if item_type == "services":
            title = item.get("title", {}).get(language, "")
        elif item_type == "channels":
            title = item.get("title", {}).get(language, "")
        elif item_type == "numbers":
            title = item.get("number", "")
        
        status = "🟢" if item.get("status") == "active" else "🔴"
        keyboard.append([InlineKeyboardButton(f"{status} {title}", callback_data=f"admin_{action}_{item_type}_{item_id}")])
    
    keyboard.append([InlineKeyboardButton(get_text("back", language), callback_data="admin")])
    return InlineKeyboardMarkup(keyboard)

def confirm_keyboard(action, item_id, language="ar"):
    """Confirmation keyboard"""
    keyboard = [
        [
            InlineKeyboardButton("✅ نعم" if language == "ar" else "✅ Yes" if language == "en" else "✅ بله", 
                               callback_data=f"confirm_{action}_{item_id}"),
            InlineKeyboardButton("❌ لا" if language == "ar" else "❌ No" if language == "en" else "❌ خیر", 
                               callback_data="admin")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def cancel_keyboard(language="ar"):
    """Cancel keyboard for forms"""
    return ReplyKeyboardMarkup([
        [KeyboardButton("إلغاء" if language == "ar" else "Cancel" if language == "en" else "لغو")]
    ], resize_keyboard=True, one_time_keyboard=True)

def coupon_keyboard(coupon_code, language="ar"):
    """Keyboard for coupon management"""
    keyboard = [
        [
            InlineKeyboardButton(get_text("complete_coupon", language), callback_data=f"complete_coupon_{coupon_code}"),
            InlineKeyboardButton(get_text("cancel_coupon", language), callback_data=f"cancel_coupon_{coupon_code}")
        ],
        [InlineKeyboardButton(get_text("back", language), callback_data="admin")]
    ]
    return InlineKeyboardMarkup(keyboard)

def coupon_confirmation_keyboard(coupon_code, language="ar"):
    """Keyboard for coupon purchase confirmation"""
    keyboard = [
        [InlineKeyboardButton("✅ تأكيد الشراء" if language == "ar" else "✅ Confirm Purchase" if language == "en" else "✅ تأیید خرید",
                            callback_data=f"confirm_purchase_{coupon_code}")],
        [InlineKeyboardButton(get_text("back", language), callback_data="main_menu")]
    ]
    return InlineKeyboardMarkup(keyboard)
